body {
    font-family: 'Arial', sans-serif;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    margin: 0;
    background-color: #f5f5dc;
}

.game-container {
    text-align: center;
}

.board {
    display: grid;
    grid-template-columns: repeat(9, 60px);
    grid-template-rows: repeat(10, 60px);
    gap: 1px;
    background-color: #000;
    border: 2px solid #8b4513;
    margin: 20px auto;
}

.cell {
    background-color: #f0d9b5;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
}

.piece {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    cursor: pointer;
    user-select: none;
}

.red {
    background-color: #ff4d4d;
    color: white;
}

.black {
    background-color: #333;
    color: white;
}

.river {
    background-color: #add8e6;
}

.palace {
    background-color: #f0d9b5;
    position: relative;
}

.palace::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    border: 2px dashed #8b4513;
}

.game-info {
    margin-top: 20px;
}

#turn-indicator {
    font-size: 1.2em;
    margin-bottom: 10px;
}

#reset-btn {
    padding: 8px 16px;
    background-color: #8b4513;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

#reset-btn:hover {
    background-color: #a0522d;
}