// Game constants
const BOARD_WIDTH = 9;
const BOARD_HEIGHT = 10;
const PLAYER_RED = 'red';
const PLAYER_BLACK = 'black';

// Game state
let currentPlayer = PLAYER_RED;
let selectedPiece = null;
let board = [];

// Initialize the game
function initGame() {
    createBoard();
    setupPieces();
    renderBoard();
    document.getElementById('reset-btn').addEventListener('click', resetGame);
}

// Create empty board
function createBoard() {
    const boardElement = document.getElementById('board');
    boardElement.innerHTML = '';
    board = Array(BOARD_HEIGHT).fill().map(() => Array(BOARD_WIDTH).fill(null));

    for (let row = 0; row < BOARD_HEIGHT; row++) {
        for (let col = 0; col < BOARD_WIDTH; col++) {
            const cell = document.createElement('div');
            cell.className = 'cell';
            cell.dataset.row = row;
            cell.dataset.col = col;

            // Mark river area (rows 4-5)
            if (row >= 4 && row <= 5) {
                cell.classList.add('river');
            }

            // Mark palaces (rows 0-2 and 7-9, cols 3-5)
            if ((row <= 2 || row >= 7) && col >= 3 && col <= 5) {
                cell.classList.add('palace');
            }

            cell.addEventListener('click', () => handleCellClick(row, col));
            boardElement.appendChild(cell);
        }
    }
}

// Set up initial piece positions
function setupPieces() {
    // Red pieces (bottom)
    placePiece(0, 0, 'chariot', PLAYER_RED);
    placePiece(0, 1, 'horse', PLAYER_RED);
    placePiece(0, 2, 'elephant', PLAYER_RED);
    placePiece(0, 3, 'advisor', PLAYER_RED);
    placePiece(0, 4, 'general', PLAYER_RED);
    placePiece(0, 5, 'advisor', PLAYER_RED);
    placePiece(0, 6, 'elephant', PLAYER_RED);
    placePiece(0, 7, 'horse', PLAYER_RED);
    placePiece(0, 8, 'chariot', PLAYER_RED);
    placePiece(2, 1, 'cannon', PLAYER_RED);
    placePiece(2, 7, 'cannon', PLAYER_RED);
    placePiece(3, 0, 'soldier', PLAYER_RED);
    placePiece(3, 2, 'soldier', PLAYER_RED);
    placePiece(3, 4, 'soldier', PLAYER_RED);
    placePiece(3, 6, 'soldier', PLAYER_RED);
    placePiece(3, 8, 'soldier', PLAYER_RED);

    // Black pieces (top)
    placePiece(9, 0, 'chariot', PLAYER_BLACK);
    placePiece(9, 1, 'horse', PLAYER_BLACK);
    placePiece(9, 2, 'elephant', PLAYER_BLACK);
    placePiece(9, 3, 'advisor', PLAYER_BLACK);
    placePiece(9, 4, 'general', PLAYER_BLACK);
    placePiece(9, 5, 'advisor', PLAYER_BLACK);
    placePiece(9, 6, 'elephant', PLAYER_BLACK);
    placePiece(9, 7, 'horse', PLAYER_BLACK);
    placePiece(9, 8, 'chariot', PLAYER_BLACK);
    placePiece(7, 1, 'cannon', PLAYER_BLACK);
    placePiece(7, 7, 'cannon', PLAYER_BLACK);
    placePiece(6, 0, 'soldier', PLAYER_BLACK);
    placePiece(6, 2, 'soldier', PLAYER_BLACK);
    placePiece(6, 4, 'soldier', PLAYER_BLACK);
    placePiece(6, 6, 'soldier', PLAYER_BLACK);
    placePiece(6, 8, 'soldier', PLAYER_BLACK);
}

// Place a piece on the board
function placePiece(row, col, type, player) {
    board[row][col] = { type, player };
}

// Render the board with pieces
function renderBoard() {
    const cells = document.querySelectorAll('.cell');
    cells.forEach(cell => {
        cell.innerHTML = '';
        const row = parseInt(cell.dataset.row);
        const col = parseInt(cell.dataset.col);
        const piece = board[row][col];

        if (piece) {
            const pieceElement = document.createElement('div');
            pieceElement.className = `piece ${piece.player}`;
            pieceElement.textContent = getPieceSymbol(piece.type);
            cell.appendChild(pieceElement);
        }
    });

    document.getElementById('turn-indicator').textContent = `${currentPlayer === PLAYER_RED ? 'Red' : 'Black'}'s Turn`;
}

// Get Unicode symbol for piece type
function getPieceSymbol(type) {
    const symbols = {
        general: '将',
        advisor: '士',
        elephant: '相',
        horse: '马',
        chariot: '车',
        cannon: '炮',
        soldier: '兵'
    };
    return symbols[type] || '';
}

// Handle cell click
function handleCellClick(row, col) {
    const piece = board[row][col];
    
    if (selectedPiece) {
        // Try to move
        if (isValidMove(selectedPiece.row, selectedPiece.col, row, col)) {
            movePiece(selectedPiece.row, selectedPiece.col, row, col);
            selectedPiece = null;
            currentPlayer = currentPlayer === PLAYER_RED ? PLAYER_BLACK : PLAYER_RED;
            renderBoard();
        } else {
            // Invalid move, select new piece
            selectedPiece = piece ? { row, col, player: piece.player } : null;
        }
    } else if (piece && piece.player === currentPlayer) {
        // Select piece
        selectedPiece = { row, col, player: piece.player };
    }
}

// Check if move is valid (simplified for now)
function isValidMove(fromRow, fromCol, toRow, toCol) {
    const piece = board[fromRow][fromCol];
    if (!piece) return false;
    if (board[toRow][toCol] && board[toRow][toCol].player === piece.player) return false;

    // TODO: Implement proper movement rules for each piece type
    return true;
}

// Move piece
function movePiece(fromRow, fromCol, toRow, toCol) {
    board[toRow][toCol] = board[fromRow][fromCol];
    board[fromRow][fromCol] = null;
}

// Reset game
function resetGame() {
    currentPlayer = PLAYER_RED;
    selectedPiece = null;
    initGame();
}

// Start the game
document.addEventListener('DOMContentLoaded', initGame);